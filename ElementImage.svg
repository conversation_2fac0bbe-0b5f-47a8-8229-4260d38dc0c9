<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Motor/Valve Symbol Control Icon -->

<svg
   width="210mm"
   height="87mm"
   viewBox="0 0 210 87"
   version="1.1"
   id="svg5"
   xmlns="http://www.w3.org/2000/svg">
  <defs
     id="defs2" />
  <g
     id="layer1">
    <!-- Background -->
    <rect
       style="fill:#f0f0f0;fill-opacity:1;stroke:#cccccc;stroke-width:1"
       id="background"
       width="210"
       height="87"
       x="0"
       y="0" />

    <!-- Motor Symbol -->
    <circle
       style="fill:#808080;fill-opacity:1;stroke:#000000;stroke-width:2"
       id="motor-circle"
       cx="105"
       cy="43.5"
       r="25" />

    <!-- Motor Label -->
    <text
       x="105"
       y="50"
       text-anchor="middle"
       font-family="Arial, sans-serif"
       font-size="18"
       font-weight="bold"
       fill="#FFFFFF"
       id="motor-text">M</text>

    <!-- Valve Symbol (alternative) -->
    <polygon
       style="fill:#606060;fill-opacity:0.7;stroke:#000000;stroke-width:1.5"
       id="valve-diamond"
       points="150,25 170,43.5 150,62 130,43.5" />

    <!-- Valve Stem -->
    <line
       x1="150"
       y1="25"
       x2="150"
       y2="15"
       stroke="#000000"
       stroke-width="2"
       id="valve-stem" />

    <!-- State indicators (8 states) -->
    <circle cx="30" cy="15" r="3" fill="#808080" opacity="0.8" />
    <circle cx="45" cy="15" r="3" fill="#00FF00" opacity="0.8" />
    <circle cx="60" cy="15" r="3" fill="#FF0000" opacity="0.8" />
    <circle cx="75" cy="15" r="3" fill="#FFFF00" opacity="0.8" />
    <circle cx="90" cy="15" r="3" fill="#0000FF" opacity="0.8" />
    <circle cx="105" cy="15" r="3" fill="#FF8000" opacity="0.8" />
    <circle cx="120" cy="15" r="3" fill="#800080" opacity="0.8" />
    <circle cx="135" cy="15" r="3" fill="#00FFFF" opacity="0.8" />
    <circle cx="150" cy="15" r="3" fill="#FF00FF" opacity="0.8" />

    <!-- Custom SVG indicator -->
    <text x="180" y="20" font-family="Arial, sans-serif" font-size="10" fill="#333">SVG</text>
  </g>
</svg>
