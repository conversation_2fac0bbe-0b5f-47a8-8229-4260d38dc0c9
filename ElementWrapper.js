var HTML5DemoButtonElementWrapper;

(function ()
{
	/* This HTML5 symbol control displays a motor or valve symbol that changes color based on an integer state value.
	* The symbol color indicates different operational states of the equipment.*/
	HTML5DemoButtonElementWrapper = function(idGenerator)
	{
        this.domNode = document.createElement("div");
		this.domNode.className = "symbol-container";
		this.domNode.style.width = "100%";
		this.domNode.style.height = "100%";
		this.domNode.style.overflow = "visible";

		// Create SVG element for the symbol
		this.svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
		this.svgElement.setAttribute("class", "symbol");
		this.svgElement.setAttribute("viewBox", "0 0 100 100");
		this.svgElement.setAttribute("preserveAspectRatio", "xMidYMid meet");

		this.domNode.appendChild(this.svgElement);
		document.body.appendChild(this.domNode);

		// Initialize with default motor symbol
		this.createMotorSymbol();
		this.currentState = 0;
	};
		
	HTML5DemoButtonElementWrapper.prototype =
	{
		createMotorSymbol: function()
		{
			// Clear existing content
			this.svgElement.innerHTML = '';

			// Create motor symbol (circle with M inside)
			var circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
			circle.setAttribute("cx", "50");
			circle.setAttribute("cy", "50");
			circle.setAttribute("r", "40");
			circle.setAttribute("fill", "#808080");
			circle.setAttribute("stroke", "#000000");
			circle.setAttribute("stroke-width", "2");
			circle.setAttribute("id", "motor-body");

			var text = document.createElementNS("http://www.w3.org/2000/svg", "text");
			text.setAttribute("x", "50");
			text.setAttribute("y", "58");
			text.setAttribute("text-anchor", "middle");
			text.setAttribute("font-family", "Arial, sans-serif");
			text.setAttribute("font-size", "24");
			text.setAttribute("font-weight", "bold");
			text.setAttribute("fill", "#FFFFFF");
			text.textContent = "M";

			this.svgElement.appendChild(circle);
			this.svgElement.appendChild(text);
		},

		createValveSymbol: function()
		{
			// Clear existing content
			this.svgElement.innerHTML = '';

			// Create valve symbol (diamond shape)
			var diamond = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
			diamond.setAttribute("points", "50,10 90,50 50,90 10,50");
			diamond.setAttribute("fill", "#808080");
			diamond.setAttribute("stroke", "#000000");
			diamond.setAttribute("stroke-width", "2");
			diamond.setAttribute("id", "valve-body");

			// Add valve stem
			var stem = document.createElementNS("http://www.w3.org/2000/svg", "line");
			stem.setAttribute("x1", "50");
			stem.setAttribute("y1", "10");
			stem.setAttribute("x2", "50");
			stem.setAttribute("y2", "0");
			stem.setAttribute("stroke", "#000000");
			stem.setAttribute("stroke-width", "3");

			this.svgElement.appendChild(diamond);
			this.svgElement.appendChild(stem);
		},

		setState: function(value)
		{
			this.currentState = parseInt(value) || 0;
			this.updateSymbolColor();
		},

		updateSymbolColor: function()
		{
			var symbolBody = this.svgElement.querySelector("#motor-body, #valve-body");
			if (!symbolBody) return;

			var color;
			switch(this.currentState) {
				case 0: color = "#808080"; break; // Gray - Off/Inactive
				case 1: color = "#00FF00"; break; // Green - Running/Open
				case 2: color = "#FF0000"; break; // Red - Fault/Closed
				case 3: color = "#FFFF00"; break; // Yellow - Warning
				case 4: color = "#0000FF"; break; // Blue - Manual mode
				default: color = "#808080"; break; // Default gray
			}

			symbolBody.setAttribute("fill", color);
		},

		setSymbolType: function(value)
		{
			if (value === "valve") {
				this.createValveSymbol();
			} else {
				this.createMotorSymbol();
			}
			this.updateSymbolColor();
		}
	};
}());