var HTML5DemoButtonElementWrapper;

(function ()
{
	/* This HTML5 symbol control displays a motor or valve symbol that changes color based on an integer state value.
	* The symbol color indicates different operational states of the equipment.*/
	HTML5DemoButtonElementWrapper = function(idGenerator)
	{
        this.domNode = document.createElement("div");
		this.domNode.className = "symbol-container";
		this.domNode.style.width = "100%";
		this.domNode.style.height = "100%";
		this.domNode.style.overflow = "visible";

		// Create SVG element for the symbol
		this.svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
		this.svgElement.setAttribute("class", "symbol");
		this.svgElement.setAttribute("viewBox", "0 0 100 100");
		this.svgElement.setAttribute("preserveAspectRatio", "xMidYMid meet");

		this.domNode.appendChild(this.svgElement);
		document.body.appendChild(this.domNode);

		// Initialize with default motor symbol
		this.createMotorSymbol();
		this.currentState = 0;
	};
		
	HTML5DemoButtonElementWrapper.prototype =
	{
		createMotorSymbol: function()
		{
			// Clear existing content
			this.svgElement.innerHTML = '';

			// Create motor symbol (circle with M inside)
			var circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
			circle.setAttribute("cx", "50");
			circle.setAttribute("cy", "50");
			circle.setAttribute("r", "40");
			circle.setAttribute("fill", "#808080");
			circle.setAttribute("stroke", "#000000");
			circle.setAttribute("stroke-width", "2");
			circle.setAttribute("id", "motor-body");

			var text = document.createElementNS("http://www.w3.org/2000/svg", "text");
			text.setAttribute("x", "50");
			text.setAttribute("y", "58");
			text.setAttribute("text-anchor", "middle");
			text.setAttribute("font-family", "Arial, sans-serif");
			text.setAttribute("font-size", "24");
			text.setAttribute("font-weight", "bold");
			text.setAttribute("fill", "#FFFFFF");
			text.textContent = "M";

			this.svgElement.appendChild(circle);
			this.svgElement.appendChild(text);
		},

		createValveSymbol: function()
		{
			// Clear existing content
			this.svgElement.innerHTML = '';

			// Create valve symbol (diamond shape)
			var diamond = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
			diamond.setAttribute("points", "50,10 90,50 50,90 10,50");
			diamond.setAttribute("fill", "#808080");
			diamond.setAttribute("stroke", "#000000");
			diamond.setAttribute("stroke-width", "2");
			diamond.setAttribute("id", "valve-body");

			// Add valve stem
			var stem = document.createElementNS("http://www.w3.org/2000/svg", "line");
			stem.setAttribute("x1", "50");
			stem.setAttribute("y1", "10");
			stem.setAttribute("x2", "50");
			stem.setAttribute("y2", "0");
			stem.setAttribute("stroke", "#000000");
			stem.setAttribute("stroke-width", "3");

			this.svgElement.appendChild(diamond);
			this.svgElement.appendChild(stem);
		},

		setState: function(value)
		{
			this.currentState = parseInt(value) || 0;
			this.updateSymbolColor();
		},

		updateSymbolColor: function()
		{
			var symbolBody = this.svgElement.querySelector("#motor-body, #valve-body, #custom-symbol");
			if (!symbolBody) {
				// If no specific body found, try to color the entire SVG
				var allPaths = this.svgElement.querySelectorAll("path, circle, rect, polygon, ellipse");
				if (allPaths.length > 0) {
					symbolBody = allPaths[0]; // Color the first shape found
				}
			}
			if (!symbolBody) return;

			var color;
			switch(this.currentState) {
				case 0: color = "#808080"; break; // Gray - Off/Inactive
				case 1: color = "#00FF00"; break; // Green - Running/Open
				case 2: color = "#FF0000"; break; // Red - Fault/Closed
				case 3: color = "#FFFF00"; break; // Yellow - Warning
				case 4: color = "#0000FF"; break; // Blue - Manual mode
				case 5: color = "#FF8000"; break; // Orange - Maintenance
				case 6: color = "#800080"; break; // Purple - Test mode
				case 7: color = "#00FFFF"; break; // Cyan - Standby
				case 8: color = "#FF00FF"; break; // Magenta - Emergency
				default: color = "#808080"; break; // Default gray
			}

			symbolBody.setAttribute("fill", color);
		},

		setSymbolType: function(value)
		{
			if (value === "valve") {
				this.createValveSymbol();
			} else if (value === "motor") {
				this.createMotorSymbol();
			} else {
				// Treat as custom SVG path
				this.loadCustomSVG(value);
			}
			this.updateSymbolColor();
		},

		loadCustomSVG: function(svgPath)
		{
			var self = this;

			// If it's a data URL or inline SVG, handle it directly
			if (svgPath.startsWith('data:') || svgPath.startsWith('<svg')) {
				this.setCustomSVGContent(svgPath);
				return;
			}

			// Try to load from URL/path
			var xhr = new XMLHttpRequest();
			xhr.open('GET', svgPath, true);
			xhr.onreadystatechange = function() {
				if (xhr.readyState === 4) {
					if (xhr.status === 200) {
						self.setCustomSVGContent(xhr.responseText);
					} else {
						console.warn('Failed to load SVG from: ' + svgPath);
						// Fallback to motor symbol
						self.createMotorSymbol();
						self.updateSymbolColor();
					}
				}
			};
			xhr.send();
		},

		setCustomSVGContent: function(svgContent)
		{
			// Clear existing content
			this.svgElement.innerHTML = '';

			var tempDiv = document.createElement('div');

			if (svgContent.startsWith('data:')) {
				// Handle data URL
				var img = document.createElement('img');
				img.src = svgContent;
				img.style.width = '100%';
				img.style.height = '100%';
				this.svgElement.appendChild(img);
			} else {
				// Handle SVG markup
				tempDiv.innerHTML = svgContent;
				var svgNode = tempDiv.querySelector('svg');

				if (svgNode) {
					// Copy all child elements from the loaded SVG
					while (svgNode.firstChild) {
						this.svgElement.appendChild(svgNode.firstChild);
					}

					// Copy important attributes
					if (svgNode.getAttribute('viewBox')) {
						this.svgElement.setAttribute('viewBox', svgNode.getAttribute('viewBox'));
					}

					// Add ID to first colorable element for state coloring
					var colorableElements = this.svgElement.querySelectorAll('path, circle, rect, polygon, ellipse');
					if (colorableElements.length > 0) {
						colorableElements[0].setAttribute('id', 'custom-symbol');
					}
				} else {
					console.warn('Invalid SVG content provided');
					this.createMotorSymbol();
				}
			}
		},

		setCustomSVGPath: function(value)
		{
			if (value && value.trim() !== '') {
				this.customSVGPath = value;
				this.loadCustomSVG(value);
			}
		}
	};
}());