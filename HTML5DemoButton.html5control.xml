<?xml version="1.0" encoding="utf-8"?>
<Html5Control>
	<General>
		<Name>HTML5DemoButton</Name>
		<Company>DemoCompany</Company>
		<Description>HTML5 Motor/Valve Symbol Control with state-based coloring</Description>
		<Version>*******</Version>
		<Image>ElementImage.svg</Image>
		<Category>HTML5 Demo Controls</Category>
		<ControlText>ControlTexts</ControlText>
		<Files>
			<File>ButtonStyle.css</File>
			<File>ElementWrapper.js</File>
		</Files>
	</General>
	<ControlProperties>
		<Single xml:space="preserve" Type="{af53e824-e51f-4be1-b210-d001ef8efbd1}" Method="IArchivable">
  <List2 Name="HierarchicalProperties">
    <Single Type="{1626d958-cc88-4833-ad94-2fe3d1ca1a4a}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">0</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">Property</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">State</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string">setState</Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
    <Single Type="{1626d958-cc88-4833-ad94-2fe3d1ca1a4a}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">6</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">Property</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">SymbolType</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string">setSymbolType</Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string">motor</Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
    <Single Type="{1626d958-cc88-4833-ad94-2fe3d1ca1a4a}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">8</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">Property</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">CustomSVGPath</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string">setCustomSVGPath</Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>

    <Single Type="{b53945e6-babc-4750-9a2d-8bb79e826e05}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">m_StaticPosition</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">m_StaticPosition</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
    <Single Type="{b53945e6-babc-4750-9a2d-8bb79e826e05}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">2</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">m_StaticCenter</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">m_StaticCenter</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
    <Single Type="{b53945e6-babc-4750-9a2d-8bb79e826e05}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">3</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">m_pAbsoluteAnimation</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">m_pAbsoluteAnimation</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
    <Single Type="{b53945e6-babc-4750-9a2d-8bb79e826e05}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">4</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">m_pStateVariables</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">m_pStateVariables</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">1</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
    <Single Type="{b53945e6-babc-4750-9a2d-8bb79e826e05}" Method="IArchivable">
      <Single Name="HierarchicalPropertiesNodeBaseNodeId" Type="long">5</Single>
      <Single Name="HierarchicalPropertiesNodeBaseParent" Type="long">-1</Single>
      <Single Name="HierarchicalPropertiesNodeBaseNodeName" Type="string">InputConfiguration</Single>
      <Single Name="HierarchicalPropertiesNodeBaseDataEntry" Type="{36c29c7f-f2b6-44b2-b62f-0a5c394cfbfe}" Method="IArchivable">
        <Single Name="HierarchicalPropertiesDataEntryName" Type="string">m_pInputHandler</Single>
        <Single Name="HierarchicalPropertiesDataEntryTextId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryVariable" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryDescriptionId" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryPropertyType" Type="int">0</Single>
        <Single Name="HierarchicalPropertiesDataEntryVariableType" Type="string"></Single>
        <Single Name="HierarchicalPropertiesDataEntryMappingType" Type="int">0</Single>
        <Single Name="HierarchicalGenericValue" Type="{d0c829a1-7995-459f-862b-f92b21ac994f}" Method="IArchivable">
          <Single Name="HierarchicalGenericValue_Value" Type="string"></Single>
          <Single Name="HierarchicalGenericValue_EditorType" Type="int">1</Single>
        </Single>
      </Single>
    </Single>
  </List2>
</Single>
	</ControlProperties>
</Html5Control>